import sys
import shutil
from pathlib import Path
from datetime import datetime
import xml.etree.ElementTree as ET

from PySide6.QtCore import Qt, QByteArray
from PySide6.QtWidgets import (
    QApplication,
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QLabel,
    QScrollArea,
)
from PySide6.QtSvgWidgets import QSvgWidget

def get_path(relative_path):
    """ Converts a relative path to an absolute path. """
    return Path(relative_path).resolve()

def extract_price(svg_path):
    """ Extracts the price from a filename. """
    try:
        filename = svg_path.name
        parts = filename.split('-')
        for part in parts:
            if part.startswith('price'):
                return part[5:]
    except Exception:
        pass
    return "N/A"

def get_cropped_svg_data(svg_path, crop_size):
    """ Reads and crops SVG data in memory. """
    try:
        ET.register_namespace("", "http://www.w3.org/2000/svg")
        tree = ET.parse(svg_path)
        root = tree.getroot()
        root.set('viewBox', f'0 0 {crop_size} {crop_size}')
        root.set('width', str(crop_size))
        root.set('height', str(crop_size))
        cropped_svg_bytes = ET.tostring(root, encoding='utf-8')
        return QByteArray(cropped_svg_bytes)
    except Exception as e:
        print(f"Error cropping SVG {svg_path.name}: {e}")
        return None

class ResultsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Results Page")
        self.resize(800, 700)
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        self.page_layout = QVBoxLayout(central_widget)
        self.show_results_page()

    def show_results_page(self):
        print("Attempting to load and display SVG results...")
        # ... (file loading and error handling code remains the same) ...
        output_dir_most_expensive = get_path("outputs/single_bin/most_expensive")
        output_dir_single_bin = get_path("outputs/single_bin/")

        if not output_dir_most_expensive.exists() or not output_dir_single_bin.exists():
            error_msg = f"Error: Output directory not found!\n\nSearched in: {output_dir_single_bin.parent}"
            error_label = QLabel(error_msg)
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("font-size: 16px; color: red;")
            self.page_layout.addWidget(error_label)
            return

        image_files_most_expensive = [f for f in output_dir_most_expensive.iterdir() if f.suffix.lower() == '.svg']
        image_files_single_bin = [f for f in output_dir_single_bin.iterdir() if f.suffix.lower() == '.svg']

        if not image_files_most_expensive:
            error_label = QLabel("Error: No SVG found in the 'most_expensive' directory!")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("font-size: 16px; color: red;")
            self.page_layout.addWidget(error_label)
            return

        most_expensive_svg_path = image_files_most_expensive[0]
        all_svgs_map = {most_expensive_svg_path.name: most_expensive_svg_path}
        for svg_file in image_files_single_bin:
            if svg_file.name not in all_svgs_map:
                all_svgs_map[svg_file.name] = svg_file
        all_svg_files_to_display = list(all_svgs_map.values())

        saved_svg_dir = get_path("saved_svg")
        saved_svg_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_svg_path = saved_svg_dir / f"output_{timestamp}.svg"
        shutil.copy(most_expensive_svg_path, saved_svg_path)
        print(f"Copied most expensive SVG to {saved_svg_path}")

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        scroll_content_widget = QWidget()
        content_layout = QVBoxLayout(scroll_content_widget)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop)
        content_layout.setSpacing(4)

        title_label = QLabel("Top most expensive Outputs")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin-bottom: 50px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(title_label)

        CROP_SIZE = 700

        # --- Display Logic ---

        # 1. Always display the "Most Expensive" output
        most_expensive_path = all_svg_files_to_display[0]
        # ... (code to display the most expensive SVG and its price remains the same) ...
        subtitle_label = QLabel("Most Expensive:")
        subtitle_label.setStyleSheet("font-size: 36px; font-weight: bold; margin-bottom: 20px;")
        content_layout.addWidget(subtitle_label, alignment=Qt.AlignmentFlag.AlignCenter)

        cropped_data = get_cropped_svg_data(most_expensive_path, CROP_SIZE)
        svg_widget = QSvgWidget()
        if cropped_data:
            svg_widget.load(cropped_data)
        svg_widget.setFixedSize(CROP_SIZE, CROP_SIZE)
        content_layout.addWidget(svg_widget, alignment=Qt.AlignmentFlag.AlignCenter)

        price = extract_price(most_expensive_path)
        price_label = QLabel(f"Price: {price}")
        price_label.setStyleSheet("color: green; font-size: 24px; font-weight: bold; margin-bottom: 15px;")
        price_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(price_label)


        # 2. Handle "Other Outputs"
        other_outputs = all_svg_files_to_display[1:]

        # --- NEW: Sort the list by price in descending order ---
        sorted_other_outputs = sorted(
            other_outputs,
            key=lambda path: int(p) if (p := extract_price(path)).isdigit() else 0,
            reverse=True
        )
        # --- END NEW ---

        # Use the newly sorted list for the rest of the logic
        num_other_outputs = len(sorted_other_outputs)

        if num_other_outputs > 0:
            if num_other_outputs > 5:
                display_title = "Top 5"
                outputs_to_show = sorted_other_outputs[:5]
            else:
                display_title = f"Top {num_other_outputs}"
                outputs_to_show = sorted_other_outputs

            other_subtitle_label = QLabel(display_title)
            other_subtitle_label.setStyleSheet("font-size: 36px; font-weight: bold; margin-top: 50px; margin-bottom: 20px;")
            content_layout.addWidget(other_subtitle_label, alignment=Qt.AlignmentFlag.AlignCenter)

            for svg_path in outputs_to_show:
                # ... (code to display other SVGs and their prices remains the same) ...
                cropped_data = get_cropped_svg_data(svg_path, CROP_SIZE)
                svg_widget = QSvgWidget()
                if cropped_data:
                    svg_widget.load(cropped_data)
                svg_widget.setFixedSize(CROP_SIZE, CROP_SIZE)
                content_layout.addWidget(svg_widget, alignment=Qt.AlignmentFlag.AlignCenter)

                price = extract_price(svg_path)
                price_label = QLabel(f"Price: {price}")
                price_label.setStyleSheet("color: green; font-size: 24px; font-weight: bold; margin-bottom: 30px;")
                price_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                content_layout.addWidget(price_label)

        # ... (final success message code remains the same) ...
        short_path = "/".join(saved_svg_path.parts[-2:])
        success_label = QLabel(f"Top SVG saved successfully in 'saved_svg' as: {short_path}")
        success_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        success_label.setStyleSheet("color: green; font-weight: bold; font-size: 16px; margin-top: 10px;")
        success_label.setWordWrap(False)
        success_label.setMaximumWidth(1000)
        content_layout.addWidget(success_label, alignment=Qt.AlignmentFlag.AlignCenter)


        scroll_area.setWidget(scroll_content_widget)
        self.page_layout.addWidget(scroll_area)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ResultsWindow()
    window.show()
    sys.exit(app.exec())