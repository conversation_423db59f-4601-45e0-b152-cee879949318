import os
import random
import shutil
import pandas as pd
from sqlalchemy import true
import svgwrite
import variables as var
import calc_max_number_of_sizes
import sys


def list_from_max(q_max):
    q_list = list(range(q_max+1))
    for q in q_list:
        q_list[q_list.index(q)] = str(q)
    
    return q_list


def dodo_it(X, Y):
    res = []
    for i in X:
        for j in Y: 
            res.append(i+'@'+j)
    return res


def random_color():
    rr = random.randint(0, 100)
    gg = random.randint(0, 100)
    bb = random.randint(0, 100)

    return rr, gg, bb


def delet_folder_contents(path):
    if not os.path.exists(path):
        os.makedirs(path)
    else:
        shutil.rmtree(path)
        os.makedirs(path)



if __name__ == '__main__' or __name__ == 'gen_merged_all_states':
    #print('name______________________________ :', __name__)
    delet_folder_contents(var.MERGED_SVG_FOLDER_DIR)

    df = pd.read_excel(var.PREPROCESSED_EXCEL_DIR, sheet_name=['size'])
    df_size = df.get('size')

    #print("******"*10)
    #print(df_size)
    #print("******"*10)

    # df_size['A'] = df_size['X'] * df_size['Y']
    # df_size = df_size.sort_values(by=['A'], ascending=False)
    df_size_list = df_size.values.tolist()
    max_number_of_each_size_list = df_size['Q_max'].values.tolist()

    #print(max_number_of_each_size_list)

    # ask_to_cont = input("Continue ? (yes/no)   ---   0")

    states_size_list = []
    for q in max_number_of_each_size_list:
        states_size_list.append(list_from_max(q))

    #print("$"*100)
    #print(states_size_list)

    for s_index in range(len(states_size_list)):
        if s_index == 0:
            pass
        else:
            states_size_list[0] = dodo_it(states_size_list[0], states_size_list[s_index])

    all_states_size_list = states_size_list[0]

    print("states counte = " + str(len(all_states_size_list)))
    sys.stdout.flush()

    # ask_to_cont = input("Continue ? (yes/no)   ---   1")

    # print(all_states_size_list)

    # ask_to_cont = input("Continue ? (yes/no)   ---   1.22")

    # for state in all_states_size_list:
    #     print(state)


    # ask_to_cont = input("Continue ? (yes/no)   ---   1.4")

    #print(max_number_of_each_size_list)

    # ask_to_cont = input("Continue ? (yes/no)   ---   1.5")


    for index, kkk in enumerate(max_number_of_each_size_list):
        new_state = ""
        
        for a in range(index):
            new_state += '0@'
        
        new_state += str(kkk + 1)
        
        for a in range(len(max_number_of_each_size_list) - index - 1):
            new_state += '@0'

        # print(new_state)
        all_states_size_list.append(new_state)


    # DELETE BAD STATES
    for this_index_for, this_state_for in enumerate(all_states_size_list):
        # print(this_index_for, " | ", this_state_for) 

        state_list = list(this_state_for.split('@'))
        # print(state_list)



        # for row_number in range(len(max_number_of_each_size_list)):
            

        



    # ask_to_cont = input("Continue ? (yes/no)   ---   2")

    for state in all_states_size_list:
        state_list = list(state.split('@'))
        state_list_int = [eval(i) for i in state_list]
        # print(list(state))
        # print(state_list)
        # print(state_list_int)
        my_lst_str = ''.join(map(str, state_list_int))
        # print(int(my_lst_str))

        if int(my_lst_str) == 0:  # delete all zero state
            all_states_size_list.remove(state)



    #print(len(all_states_size_list))

    # ask_to_cont = input("Continue ? (yes/no)   ---   7")
    
    accepted_states = []

    


    for state in all_states_size_list:
        state_list = list(state.split('@'))
        state_list_int = [eval(i) for i in state_list]

        df_size_copy = df_size
        df_size_copy['Q'] = state_list_int

        index_colunm_location = df_size.columns.get_loc('index')
        X_colunm_location = df_size.columns.get_loc('X')
        Y_colunm_location = df_size.columns.get_loc('Y')
        A_colunm_location = df_size.columns.get_loc('A')
        Q_colunm_location = df_size.columns.get_loc('Q')
        Price_colunm_location = df_size.columns.get_loc('Price')
        
        
        this_state_total_area = 0

        for this_row in df_size_copy.values.tolist():
            # print(this_row)
            this_rect_Q = int(this_row[Q_colunm_location])
            this_rect_A = int(this_row[A_colunm_location])
            # print("Q: ", this_rect_Q, "| A: ", this_rect_A)
            
            this_row_total_area = this_rect_A * this_rect_Q
            # print("this_row_total_area : ", this_row_total_area)
            this_state_total_area += this_row_total_area

        # print("this_state_total_area : ", this_state_total_area)
        
        
        if this_state_total_area > 0.01*calc_max_number_of_sizes.lower_limit*calc_max_number_of_sizes.using_area:
            if this_state_total_area < 0.01*calc_max_number_of_sizes.upper_limit*calc_max_number_of_sizes.using_area:
                accepted_states.append(state)
        else:
            pass


    # ask_to_cont = input("Continue ? (yes/no)   ---   20")

    print(accepted_states)
    print("size :", len(accepted_states))
    sys.stdout.flush()

    # ask_to_cont = input("Continue ? (yes/no)   ---   21")


    for state in accepted_states: # for state in all_states_size_list:
        
        
        state_list = list(state.split('@'))
        state_list_int = [eval(i) for i in state_list]

        svg_merged_file_name = var.MERGED_SVG_FOLDER_DIR + '/' + 'merged_' + state + '.svg'
        dwg = svgwrite.Drawing(svg_merged_file_name, profile='full', size=('1000%', '1000%'))
        df_size_copy = df_size
        # df_size_copy['Q'] = list(state)
        df_size_copy['Q'] = state_list_int
        #print(svg_merged_file_name)
        #print(df_size_copy)
        #print('***********************************************************')
        
        size_buff_x = 10
        size_buff_y = 10
        sum_of_Q = 0
        color_list = []
        random.seed(13)

        index_colunm_location = df_size.columns.get_loc('index')
        X_colunm_location = df_size.columns.get_loc('X')
        Y_colunm_location = df_size.columns.get_loc('Y')
        A_colunm_location = df_size.columns.get_loc('A')
        Q_colunm_location = df_size.columns.get_loc('Q')
        Price_colunm_location = df_size.columns.get_loc('Price')
        
        #print("*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-")
        #print(df_size_copy.values.tolist())
        #print("*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-")
        
        this_state_total_area = 0
        for this_row in df_size_copy.values.tolist():
            #print(this_row)
            this_rect_Q = int(this_row[Q_colunm_location])
            this_rect_A = int(this_row[A_colunm_location])
            #print("Q: ", this_rect_Q, "| A: ", this_rect_A)
            
            this_row_total_area = this_rect_A * this_rect_Q
            #print("this_row_total_area : ", this_row_total_area)
            this_state_total_area += this_row_total_area

        #print("this_state_total_area : ", this_state_total_area)

        # if this_state_total_area > calc_max_number_of_sizes.using_area*0.7 or this_state_total_area < calc_max_number_of_sizes.using_area*0.3:
        #     continue

        for this_row in df_size_copy.values.tolist():
            # print(this_row)
            this_rect_X_size = this_row[X_colunm_location]
            this_rect_Y_size = this_row[Y_colunm_location]
            this_rect_Q = int(this_row[Q_colunm_location])
            this_rect_Price = this_row[Price_colunm_location]

            sum_of_Q += this_rect_Q

            a_factor = calc_max_number_of_sizes.factor

            r, g, b = random_color()
            for num in range(this_rect_Q):
                this_id = 'X:' + str(int(this_rect_X_size/a_factor)) + '_' + 'Y:' + str(int(this_rect_Y_size/a_factor)) + '_' + 'P:' + str(this_rect_Price)
                dwg.add(dwg.rect(insert=(size_buff_x, size_buff_y), size=(this_rect_X_size, this_rect_Y_size),
                                fill=svgwrite.rgb(r, g, b, '%'), stroke='red', stroke_width=1, id=this_id))
                size_buff_x += (this_rect_X_size + 10)
            if this_rect_Q != 0:
                size_buff_y += (this_rect_Y_size + 10)
            size_buff_x = 10

        # add bin using excel

        # print(bin_points)
        # print(bin_points_moved)

        # dwg.add(dwg.polygon(points=bin_points, fill='none',
        #                     stroke='blue', stroke_width=1, id="src-node"))

        dwg.add(dwg.path([calc_max_number_of_sizes.path_d], stroke='yellow', stroke_width=2, fill='none', id='src-node'))

        # r, g, b = random_color()
        # dwg.add(dwg.polygon(points=bin_points_moved, fill=svgwrite.rgb(r, g, b, '%'),
        #                     stroke='blue', stroke_width=1))

        dwg.save()
        print("\n svg generated! - ", sum_of_Q)
        sys.stdout.flush()

