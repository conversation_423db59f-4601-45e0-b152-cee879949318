import sys
import cv2
import numpy as np
from pathlib import Path
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtWidgets import (
    QApplication,
    QLabel,
    QMainWindow,
    QPushButton,
    QVBoxLayout,
    QHBoxLayout,
    QWidget,
    QSlider,
    QGridLayout
)
from PySide6.QtGui import QPixmap, QImage



# Helper function to get platform-independent absolute paths
def get_path(relative_path):
    return Path(relative_path).resolve()


# Module-level variables for backward compatibility with other modules
# These will be updated when the Qt application saves the image
area_upper_limit = 80
area_lower_limit = 60
ppp = ""  # SVG path data

def load_threshold_data():
    """Load threshold data from file if it exists"""
    global area_upper_limit, area_lower_limit, ppp
    try:
        import json
        with open('threshold_data.json', 'r') as f:
            data = json.load(f)
            area_upper_limit = data.get('area_upper_limit', 80)
            area_lower_limit = data.get('area_lower_limit', 60)
            ppp = data.get('ppp', "")
    except (FileNotFoundError, json.JSONDecodeError):
        # File doesn't exist or is invalid, use defaults
        pass

# Load data when module is imported
load_threshold_data()


def hconcat_resize_min(im_list, interpolation=cv2.INTER_CUBIC):
    h_min = min(im.shape[0] for im in im_list)
    im_list_resize = [cv2.resize(im, (int(im.shape[1] * h_min / im.shape[0]), h_min), interpolation=interpolation)
                      for im in im_list]
    return cv2.hconcat(im_list_resize)


def save_contour_as_svg(con, image, x, y):
    smallest_x_rect = x - 20
    smallest_y_rect = y - 20

    if len(con) == 0:
        return ""

    # Start with Move command for the first point
    first_point = con[0][0]
    ppp = f'M {first_point[0] - smallest_x_rect} {first_point[1] - smallest_y_rect}'

    # Add Line commands for the rest of the points
    for i in range(1, len(con)):
        point = con[i][0]
        ppp += f' L {point[0] - smallest_x_rect} {point[1] - smallest_y_rect}'

    # Close the path
    ppp += ' Z'

    # print(ppp)
    return ppp


class ThresholdWindow(QMainWindow):
    finished = Signal()  # Signal emitted when window closes after saving

    def __init__(self, image_path=None):
        super().__init__()
        self.setWindowTitle("Threshold Image to SVG")
        self.setGeometry(100, 100, 1200, 800)

        # Initialize variables
        self.img = None
        self.img_sized = None
        self.c = None
        self.x = 0
        self.y = 0
        self.ok_to_save = False
        self.my_out = None

        # Default parameter values
        self.l = 20
        self.u = 200
        self.GaussianBlur_size = 51
        self.img_scaling_factor = 80
        self.area_upper_limit = 80
        self.area_lower_limit = 60
        self.threshold_threshold = 100

        # Load image
        if image_path:
            self.img = cv2.imread(image_path)
        else:
            self.img = cv2.imread("C:\\Users\\<USER>\\Desktop\\TEST_NEW\\scanner_v3\\Input_image\\testpics\\1.png")

        self.setup_ui()
        self.process_image()

    def setup_ui(self):
        """Set up the Qt UI with sliders and image display"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)

        # Controls layout
        controls_layout = QGridLayout()

        # Create sliders with labels
        self.create_slider_with_label(controls_layout, "Lower Threshold (l)", 0, 255, self.l, self.set_l, 0)
        self.create_slider_with_label(controls_layout, "Upper Threshold (u)", 0, 255, self.u, self.set_u, 1)
        self.create_slider_with_label(controls_layout, "Gaussian Blur Size", 1, 101, self.GaussianBlur_size, self.set_GaussianBlur_size, 2)
        self.create_slider_with_label(controls_layout, "Image Scale", 1, 100, self.img_scaling_factor, self.set_scaling_value, 3)
        # self.create_slider_with_label(controls_layout, "Area Upper Limit", 1, 100, self.area_upper_limit, self.set_area_upper_limit, 4)
        # self.create_slider_with_label(controls_layout, "Area Lower Limit", 1, 100, self.area_lower_limit, self.set_area_lower_limit, 5)

        main_layout.addLayout(controls_layout)

        # Image display area
        self.image_label = QLabel("Processing...")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("border: 12px solid red;")
        self.image_label.setMinimumSize(900, 300)
        main_layout.addWidget(self.image_label)

        # Save button
        self.save_button = QPushButton("Start Nesting")
        self.save_button.clicked.connect(self.save_image)
        self.save_button.setEnabled(False)
        main_layout.addWidget(self.save_button)

    def create_slider_with_label(self, layout, label_text, min_val, max_val, initial_val, callback, row):
        """Create a slider with label and value display"""
        label = QLabel(label_text)
        layout.addWidget(label, row, 0)

        slider = QSlider(Qt.Orientation.Horizontal)
        slider.setMinimum(min_val)
        slider.setMaximum(max_val)
        slider.setValue(initial_val)
        slider.valueChanged.connect(callback)
        layout.addWidget(slider, row, 1)

        value_label = QLabel(str(initial_val))
        layout.addWidget(value_label, row, 2)

        # Store reference to update value label
        slider.value_label = value_label

    def process_image(self):
        """Process the image with current parameters"""
        if self.img is None:
            return

        # black color boundaries [B, G, R]
        lower = [self.l, self.l, self.l]
        upper = [self.u, self.u, self.u]

        # create NumPy arrays from the boundaries
        lower = np.array(lower, dtype="uint8")
        upper = np.array(upper, dtype="uint8")

        # find the colors within the specified boundaries and apply the mask
        self.img_sized = cv2.resize(self.img, None, fx=max(self.img_scaling_factor,1)/100, fy=max(self.img_scaling_factor,1)/100)

        blur = cv2.GaussianBlur(self.img_sized,(self.GaussianBlur_size,self.GaussianBlur_size),0)
        mask = cv2.inRange(blur, lower, upper)
        output = cv2.bitwise_or(blur, blur)

        ret, thresh = cv2.threshold(mask, self.threshold_threshold, 255, 0)

        stencil = np.zeros(self.img_sized.shape)

        if (cv2.__version__[0] > '3'):
            contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        else:
            im2, contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

        if len(contours) != 0:
            self.ok_to_save = False

            # find the biggest countour (c) by the area
            self.c = max(contours, key=cv2.contourArea)
            self.x, self.y, w, h = cv2.boundingRect(self.c)
            cv2.drawContours(output, self.c, -1, 255, 3)
            cv2.rectangle(output, (self.x, self.y), (self.x + w, self.y + h), (0, 255, 255), 2)

            cntr_z = self.c
            color_z = [255, 255, 255]
            cv2.fillPoly(stencil, [cntr_z], color_z)

            cv2.circle(output, (self.x, self.y), 2, (200,255,100), 2)
            cv2.circle(output, (self.x+w, self.y), 2, (200,255,100), 2)
            cv2.circle(output, (self.x, self.y+h), 2, (200,255,100), 2)
            cv2.circle(output, (self.x+w, self.y+h), 2, (200,255,100), 2)

            out_bin = stencil[max(self.y - 50, 0):self.y + h + 50, max(self.x - 50, 0):self.x + w + 50]

            img_copy = self.img_sized.copy()

            # The Blue Bounding box on the left hand side image.
            cv2.drawContours(img_copy, self.c, -1, 255, 3)
            self.x, self.y, w, h = cv2.boundingRect(self.c)
            img_copy = cv2.rectangle(img_copy,(self.x,self.y),(self.x+w,self.y+h),(0,255,0),3)
            img_copy = cv2.circle(img_copy, (self.x,self.y), 5, (0,0,255))

            if out_bin.size != 0:
                self.ok_to_save = True
                self.my_out = out_bin
                self.save_button.setEnabled(True)

            font = cv2.FONT_HERSHEY_SIMPLEX
            org = (50, 50)
            fontScale = 1
            color = (0, 0, 255)
            thickness = 3

            img_copy = cv2.putText(img_copy, str(int(h*(1/2.6))), org, font, fontScale, color, thickness, cv2.LINE_AA)

            thresh_3_channel = cv2.cvtColor(thresh, cv2.COLOR_GRAY2BGR)

            resized_img_copy = cv2.resize(img_copy, (300, 300))
            resized_thresh_3_channel = cv2.resize(thresh_3_channel, (300, 300))
            resized_stencil = cv2.resize(stencil, (300, 300))

            # Create horizontal stack of images
            numpy_vertical = np.hstack((resized_img_copy.astype('uint8'), resized_thresh_3_channel.astype('uint8'), resized_stencil.astype('uint8')))

            # Convert to Qt format and display
            self.display_image(numpy_vertical)
        else:
            self.save_button.setEnabled(False)

    def display_image(self, cv_image):
        """Convert OpenCV image to Qt format and display"""
        height, width, channel = cv_image.shape
        bytes_per_line = 3 * width
        q_image = QImage(cv_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_image)

        # Scale pixmap to fit label while maintaining aspect ratio
        scaled_pixmap = pixmap.scaled(self.image_label.size(), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        self.image_label.setPixmap(scaled_pixmap)

    def set_l(self, val):
        """Callback for lower threshold slider"""
        self.l = val
        self.sender().value_label.setText(str(val))
        self.process_image()

    def set_u(self, val):
        """Callback for upper threshold slider"""
        self.u = val
        self.sender().value_label.setText(str(val))
        self.process_image()

    def set_GaussianBlur_size(self, val):
        """Callback for Gaussian blur size slider"""
        self.GaussianBlur_size = ((val // 2) * 2) + 1
        self.sender().value_label.setText(str(self.GaussianBlur_size))
        self.process_image()

    def set_threshold_threshold(self, val):
        """Callback for threshold slider"""
        self.threshold_threshold = val
        self.sender().value_label.setText(str(val))
        self.process_image()

    def set_scaling_value(self, val):
        """Callback for image scaling slider"""
        self.img_scaling_factor = val
        self.sender().value_label.setText(str(val))
        self.process_image()

    def set_area_upper_limit(self, val):
        """Callback for area upper limit slider"""
        self.area_upper_limit = val
        self.sender().value_label.setText(str(val))
        self.process_image()

    def set_area_lower_limit(self, val):
        """Callback for area lower limit slider"""
        self.area_lower_limit = val
        self.sender().value_label.setText(str(val))
        self.process_image()

    def save_image(self):
        """Save the binary image and trigger nesting process"""
        global area_upper_limit, area_lower_limit, ppp

        if self.ok_to_save and self.my_out is not None:
            cv2.imwrite("sang_bin.png", self.my_out)

            # Update module-level variables for backward compatibility
            area_upper_limit = self.area_upper_limit
            area_lower_limit = self.area_lower_limit
            ppp = save_contour_as_svg(self.c, self.img_sized, self.x, self.y)

            # Save variables to a file for cross-process communication
            import json
            threshold_data = {
                'area_upper_limit': self.area_upper_limit,
                'area_lower_limit': self.area_lower_limit,
                'ppp': ppp
            }
            with open('threshold_data.json', 'w') as f:
                json.dump(threshold_data, f)

            print("Image saved successfully!")
            print("Starting nesting process...")

            # Emit signal and close the window to trigger the nesting process
            self.finished.emit()
            self.close()
        else:
            print("Output is not ready for export!")
    


if __name__ == "__main__":
    # Create Qt application
    app = QApplication(sys.argv)

    # Check if image path is provided as command line argument
    image_path = None
    if len(sys.argv) > 1:
        image_path = sys.argv[1]

    # Create and show the threshold window
    window = ThresholdWindow(image_path)
    window.show()

    # Run the application
    sys.exit(app.exec())