from fileinput import filename
import logging
import sys
from cv2 import merge

from selenium import webdriver
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
import time
from lxml import etree
import glob
import os
import shutil
import variables as var
from svgelements import *
from pprint import pprint
import re

nested_svg_files = glob.glob(os.path.join(var.OUTPUTS_DIR, "*.svg"))
print('nested_svg_files: ', nested_svg_files)

# Check if any nested SVG files were found
if not nested_svg_files:
    print("   No nested SVG files found in outputs directory!")
    print(f"   Checked path: {var.OUTPUTS_DIR}")
    print("   This might indicate that the nesting process didn't generate any output files.")
    sys.exit(1)

if not os.path.exists(var.SINGLE_BIN_NESTED_SVGs_FOLDER):
    os.makedirs(var.SINGLE_BIN_NESTED_SVGs_FOLDER)

# else:
#     shutil.rmtree(var.SINGLE_BIN_NESTED_SVGs_FOLDER)
#     os.makedirs(var.SINGLE_BIN_NESTED_SVGs_FOLDER)

for this_nested_file in nested_svg_files:
    print(this_nested_file)
    try:
        number_of_bins = int(this_nested_file.split('bin')[1].split('-')[0])
        if number_of_bins == 1:
            shutil.move(this_nested_file, var.SINGLE_BIN_NESTED_SVGs_FOLDER)
            print('move successfully')
    except (IndexError, ValueError) as e:
        print(f" Error parsing file name '{this_nested_file}': {e}")
        print("   File name doesn't match expected pattern 'bin{number}-...'")
        continue

def find_slab_price(svg_file):
    svg = SVG.parse(svg_file)
    list_of_id = []
    list_of_price = []
    list_of_size = []

    for this_element in svg.elements():
        if isinstance(this_element, Rect):
            this_element_id = this_element.id
            print(this_element_id)
            list_of_id.append(this_element_id)
            this_element_price = int(this_element_id.split('_P:')[1])
            print(this_element_price)
            list_of_price.append(this_element_price)
            this_element_size = this_element_id.split('_P:')[0]
            list_of_size.append(this_element_size)


    print(list_of_id)
    print(list_of_price)
    print(list_of_size)

    sum_of_prices = sum(list_of_price)
    # print(sum_of_prices)
    return sum_of_prices


svg_files = glob.glob(os.path.join(var.SINGLE_BIN_NESTED_SVGs_FOLDER, "*.svg"))
print(svg_files)

for this_svg_file in svg_files:
    this_svg_file_abs_apth = os.path.abspath(this_svg_file)
    print(this_svg_file)

    this_slab_price = find_slab_price(this_svg_file)

    print("price : ", this_slab_price)

    this_svg_file_name_without_price = os.path.basename(this_svg_file_abs_apth)
    name_splited = this_svg_file_name_without_price.split("-output")
    print(name_splited)

    new_name = name_splited[0] + '-price' + str(this_slab_price) + '-output' + name_splited[1]
    print(new_name)

    new_name_path = this_svg_file_abs_apth.replace(this_svg_file_name_without_price, '') + new_name
    print(new_name_path)

    if this_svg_file_abs_apth.find('price') == -1:
        os.rename(
            this_svg_file_abs_apth,
            new_name_path   
        )

svg_files = glob.glob(os.path.join(var.SINGLE_BIN_NESTED_SVGs_FOLDER, "*.svg"))
print(svg_files)

# Check if any single-bin SVG files were found
if not svg_files:
    print(" No single-bin SVG files found!")
    print(f"   Checked path: {var.SINGLE_BIN_NESTED_SVGs_FOLDER}")
    print("   This might indicate that no single-bin nesting results were generated.")
    sys.exit(1)

try:
    svg_files.sort(key=lambda this_svg_file_name : int(this_svg_file_name.split('-price')[1].split('-')[0]), reverse = True)
except (IndexError, ValueError) as e:
    print(f" Error sorting files by price: {e}")
    print("   Files might not have the expected naming pattern with '-price{number}-'")
    sys.exit(1)

print(svg_files)

most_expensive_slab_path = svg_files[0]
most_expensive_slab_abs_path = os.path.abspath(most_expensive_slab_path)

if not os.path.exists(var.MOST_EXSPENSIVE_SLAB_FOLDER):
    os.makedirs(var.MOST_EXSPENSIVE_SLAB_FOLDER)
else:
    shutil.rmtree(var.MOST_EXSPENSIVE_SLAB_FOLDER)
    os.makedirs(var.MOST_EXSPENSIVE_SLAB_FOLDER)  

shutil.move(
    most_expensive_slab_abs_path,
    os.path.join(os.path.abspath(var.MOST_EXSPENSIVE_SLAB_FOLDER), os.path.basename(most_expensive_slab_abs_path))
)  
print('file move successfully__________________________')
