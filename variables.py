from pathlib import Path
import os

def get_path(path_str):
    """Ensures OS-independent paths."""
    return Path(path_str).resolve()  # Converts to absolute path with the correct separator

# The directory in which downloaded files are stored
DOWNLOADS_PATH = str(Path.home() / "Downloads")

# The directory which contains the svg files for the source shapes
SRC_SHAPES_DIR = str(get_path("shapes"))

# The file that contains the shapes in different sizes
SIZES_FILE = "sizes.svg"

# The directory in which output files are stored
OUTPUTS_DIR = str(get_path("outputs"))

# Run in silent mode
SILENT_MODE = False

# Current directory
CURRENT_DIR = str(get_path("."))

# Iterations count
ITERATIONS = 2

# Input and output Excel files
INPUT_EXCEL_DIR = str(get_path("shapes.xlsx"))
PREPROCESSED_EXCEL_DIR = str(get_path("output_excel.xlsx"))

# Merged SVG folder
MERGED_SVG_FOLDER_DIR = str(get_path("merged_SVG"))

# Nested SVGs folder
SINGLE_BIN_NESTED_SVGs_FOLDER = str(get_path("outputs/single_bin"))
MOST_EXSPENSIVE_SLAB_FOLDER = str(get_path("outputs/single_bin/most_expensive"))

# Input image folder
INPUT_IMAGE_FOLDER = str(get_path("Input_image"))


# Print paths to check correctness
print(f"Downloads Path: {DOWNLOADS_PATH}")
print(f"Shapes Dir: {SRC_SHAPES_DIR}")
print(f"Outputs Dir: {OUTPUTS_DIR}")
print(f"Current Dir: {CURRENT_DIR}")
print(f"Excel File: {INPUT_EXCEL_DIR}")
print(f"SINGLE_BIN_NESTED_SVGs_FOLDER: {SINGLE_BIN_NESTED_SVGs_FOLDER}")
