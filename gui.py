import sys
import cv2
from pathlib import Path  # ✅ Use pathlib for cross-platform paths
import subprocess
import time
from datetime import datetime
from PySide6.QtCore import Qt, QTimer, Signal, QProcess, QByteArray
import xml.etree.ElementTree as ET
from PySide6.QtWidgets import (
    QApplication,
    QLabel,
    QMainWindow,
    QPushButton,
    QVBoxLayout,
    QWidget,
    QFileDialog,
    QTextEdit,
    QScrollArea
)
from PySide6.QtGui import QPixmap, QImage, QTextCursor
from PySide6.QtSvgWidgets import QSvgWidget  # ✅ Import SVG rendering widget
import shutil  # ✅ Import shutil for file copying

# ✅ Helper function to get platform-independent absolute paths
def get_path(relative_path):
    return Path(relative_path).resolve()  # Converts to absolute path with correct OS separator



class StreamRedirector:
    """ Redirects stdout and stderr to a QTextEdit widget """
    
    def __init__(self, text_widget):
        self.text_widget = text_widget

    def write(self, message):
        """ Writes output to the text widget """
        self.text_widget.append(message.strip())  # Append new message
        self.text_widget.ensureCursorVisible()  # Scroll to latest line

    def flush(self):
        """ Flush method for compatibility """
        pass


class DragDropLabel(QLabel):
    """ Label that supports drag & drop image functionality """

    def __init__(self, update_file_path_callback):
        super().__init__("Drag & Drop Image Here")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("border: 2px dashed gray; padding: 20px;")
        self.setAcceptDrops(True)
        self.update_file_path_callback = update_file_path_callback  # Callback function to update file path

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        urls = event.mimeData().urls()
        if urls:
            image_path = urls[0].toLocalFile()
            self.set_image(image_path)  
            self.update_file_path_callback(image_path)  # ✅ Update file path in SelectPage

    def set_image(self, image_path):
        """ Resizes dropped images to have a height of 500px while keeping aspect ratio """
        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            # Scale with a fixed height of 500px while keeping aspect ratio
            pixmap = pixmap.scaledToHeight(500, Qt.SmoothTransformation)
            self.setPixmap(pixmap)  # ✅ Display resized image


class SelectPage(QWidget):
    def __init__(self):
        super().__init__()
        self.webcam_window = None  
        self.file_path = None  

        self.page_layout = QVBoxLayout()
        self.select_btn = QPushButton("Select File")
        self.webcam_btn = QPushButton("Open Webcam")
        self.drag_drop = DragDropLabel(self.set_file_path) 
        self.status_output = QTextEdit('wainting for select image ...')
        self.status_output.setReadOnly(True)  # ✅ Prevent user input
        
        self.start_btn = QPushButton("Start Processing")
        self.start_btn.setEnabled(False)

        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setWidget(self.status_output)
        self.scroll_area.hide()  # ✅ Initially hide the scroll area

  

        self.select_btn.clicked.connect(self.open_file_dialog)
        self.webcam_btn.clicked.connect(self.open_webcam_page)
        self.start_btn.clicked.connect(self.start_processing)


        self.page_layout.addWidget(self.select_btn)
        self.page_layout.addWidget(self.webcam_btn)
        self.page_layout.addWidget(self.drag_drop)  
        self.page_layout.addWidget(self.start_btn)
        self.page_layout.addWidget(self.scroll_area)  # ✅ Add scrollable log area

        self.setLayout(self.page_layout)

    def start_proccess(self):
        print(self.file_path)

    def open_webcam_page(self):
        if not self.webcam_window or not self.webcam_window.isVisible():
            self.webcam_window = WebcamWindow()
            self.webcam_window.captured.connect(self.set_captured_image)  # ✅ Connect signal
            self.webcam_window.show()


    def set_captured_image(self, pixmap, file_path):
        """ Displays captured image and updates the file path """
        self.drag_drop.setPixmap(pixmap)  # ✅ Show image
        self.file_path = file_path  # ✅ Save file path
        self.start_btn.setEnabled(True)
        self.status_output.setText("Status: Image selected! \nPress on start process to start nesting.")  # ✅ Update status
        print(f"Captured Image Path: {self.file_path}")

    def set_file_path(self, file_path):
        """ Updates the file path when a file is selected or dropped """
        self.file_path = str(get_path(file_path))  # ✅ Ensure absolute, OS-friendly path
        self.start_btn.setEnabled(True)
        self.status_output.setText(f"Status: Image selected! \nPress 'Start Processing' to begin.")

    def open_file_dialog(self):
        """ Opens file dialog, resizes selected image to have a height of 500px, and updates UI """
        file_name, _ = QFileDialog.getOpenFileName(self, "Open File", "", "Images (*.png *.xpm *.jpg *.jpeg *.bmp)")
        if file_name:
            pixmap = QPixmap(file_name)
            if not pixmap.isNull():
                # Resize image to have a height of 500px while keeping aspect ratio
                pixmap = pixmap.scaledToHeight(500, Qt.SmoothTransformation)
                self.drag_drop.setPixmap(pixmap)  # ✅ Show resized image
                self.set_file_path(file_name)  # ✅ Store file path

    def start_processing(self):
        """ Starts processing and removes everything except the scrollable log output """
        if not self.file_path:
            self.status_output.append("Status: No image selected!")  # ✅ Append log
            return

        # ✅ Hide all elements except the scrollable log output
        self.select_btn.hide()
        self.webcam_btn.hide()
        self.start_btn.hide()
        self.drag_drop.hide()  

        self.scroll_area.show()  # ✅ Show scroll area only after clicking start process

        self.status_output.append("Processing started...")  # ✅ Append log

        
        # ✅ Resize scroll area
        self.scroll_area.resize(500, 500)

        # ✅ Adjust main window to fit the scroll area precisely
        parent_window = self.window()  # Get main window reference
        parent_window.adjustSize()  # Resize window to fit contents

        # ✅ Ensure the window has an exact size of 500x500 + margins
        margins = parent_window.contentsMargins()
        frame_geometry = parent_window.frameGeometry()
        extra_width = frame_geometry.width() - parent_window.width() + margins.left() + margins.right()
        extra_height = frame_geometry.height() - parent_window.height() + margins.top() + margins.bottom()

        parent_window.resize(500 + extra_width, 500 + extra_height)
        print('file_path___________________________', self.file_path)


        # Update img_path in threshold_tr_image_to_svg.py
        try:
            # Read the threshold_tr_image_to_svg.py file with UTF-8 encoding
            with open("threshold_tr_image_to_svg.py", "r", encoding="utf-8") as f:
                lines = f.readlines()

            # Escape backslashes for Windows paths
            escaped_path = self.file_path.replace("\\", "\\\\")
            with open("threshold_tr_image_to_svg.py", "w", encoding="utf-8") as f:
                for line in lines:
                    if line.strip().startswith("img_path"):
                        f.write(f'img_path = "{escaped_path}"\n')
                    elif line.strip().startswith("img = cv2.imread("):
                        f.write(f'img = cv2.imread("{escaped_path}")\n')
                    else:
                        f.write(line)

            # Start the process using QProcess
            self.process = QProcess(self)
            self.process.readyReadStandardOutput.connect(self.read_output)
            self.process.readyReadStandardError.connect(self.read_error)
            self.process.finished.connect(self.process_finished)

            python_cmd = "python3" if sys.platform != "win32" else "python"
            self.process.start(python_cmd, [str(get_path("main_faster.py"))])

        except Exception as e:
            self.status_output.append(f"Error: {e}")

    def read_output(self):
        """ Reads the standard output from the process and updates the GUI """
        output = self.process.readAllStandardOutput().data().decode()
        self.status_output.append(output.strip())  # ✅ Append logs

        # ✅ Scroll to the bottom automatically
        self.status_output.moveCursor(QTextCursor.MoveOperation.End)

    def read_error(self):
        """ Reads the error output from the process and updates the GUI """
        error = self.process.readAllStandardError().data().decode()
        if error:
            self.status_output.append(f"Error: {error.strip()}")

    def process_finished(self):
        """ Called when the process finishes execution """
        self.status_output.append("Processing completed!")  # ✅ Final update
         # ✅ Start the second script after the first one finishes
        self.status_output.append("Starting next process...")

        self.second_process = QProcess(self)
        self.second_process.readyReadStandardOutput.connect(self.read_second_output)
        self.second_process.readyReadStandardError.connect(self.read_second_error)
        self.second_process.finished.connect(self.second_process_finished)

        python_cmd = "python3" if sys.platform != "win32" else "python"
        self.second_process.start(python_cmd, [str(get_path("sort_all_nested_files.py"))])

    def read_second_output(self):
        """ Reads the standard output from the second process and updates the GUI """
        output = self.second_process.readAllStandardOutput().data().decode()
        self.status_output.append(output.strip())

    def read_second_error(self):
        """ Reads the error output from the second process and updates the GUI """
        error = self.second_process.readAllStandardError().data().decode()
        if error:
            self.status_output.append(f"Error in second process: {error.strip()}")

    def extract_price(self,svg_path):
        """ Extracts the price from a filename. """
        try:
            filename = svg_path.name
            parts = filename.split('-')
            for part in parts:
                if part.startswith('price'):
                    return part[5:]
        except Exception:
            pass
        return "N/A"
    
    def get_cropped_svg_data(self, svg_path, crop_size):
        """ Reads and crops SVG data in memory. """
        try:
            ET.register_namespace("", "http://www.w3.org/2000/svg")
            tree = ET.parse(svg_path)
            root = tree.getroot()
            root.set('viewBox', f'0 0 {crop_size} {crop_size}')
            root.set('width', str(crop_size))
            root.set('height', str(crop_size))
            cropped_svg_bytes = ET.tostring(root, encoding='utf-8')
            return QByteArray(cropped_svg_bytes)
        except Exception as e:
            print(f"Error cropping SVG {svg_path.name}: {e}")
            return None

    def second_process_finished(self):
        """Called when the second process finishes execution and displays results."""
        self.status_output.append("Second process completed! Loading results...")

        output_dir_most_expensive = get_path("outputs/single_bin/most_expensive")
        output_dir_single_bin = get_path("outputs/single_bin/")

        # --- Error Handling ---
        if not output_dir_most_expensive.exists() or not output_dir_single_bin.exists():
            self.status_output.append(f"Error: Output directory not found! CWD: {Path.cwd()}")
            return

        image_files_most_expensive = [f for f in output_dir_most_expensive.iterdir() if f.suffix.lower() == '.svg']
        image_files_single_bin = [f for f in output_dir_single_bin.iterdir() if f.suffix.lower() == '.svg']

        if not image_files_most_expensive:
            self.status_output.append("Error: No SVG found in the 'most_expensive' directory!")
            return

        # --- Prepare SVG File List ---
        most_expensive_svg_path = image_files_most_expensive[0]
        all_svgs_map = {most_expensive_svg_path.name: most_expensive_svg_path}
        for svg_file in image_files_single_bin:
            if svg_file.name not in all_svgs_map:
                all_svgs_map[svg_file.name] = svg_file
        all_svg_files_to_display = list(all_svgs_map.values())

        # --- Save the Most Expensive SVG ---
        saved_svg_dir = get_path("saved_svg")
        saved_svg_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_svg_path = saved_svg_dir / f"output_{timestamp}.svg"
        shutil.copy(most_expensive_svg_path, saved_svg_path)

        # --- Clear Existing UI ---
        for i in reversed(range(self.page_layout.count())):
            widget = self.page_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()
        
        # --- Create and Display New UI ---
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        scroll_content_widget = QWidget()
        content_layout = QVBoxLayout(scroll_content_widget)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop)
        content_layout.setSpacing(4)

        title_label = QLabel("Top most expensive Outputs")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin-bottom: 50px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(title_label)

        CROP_SIZE = 800

        # 1. Always display the "Most Expensive" output
        subtitle_label = QLabel("Most Expensive:")
        subtitle_label.setStyleSheet("font-size: 36px; font-weight: bold; margin-bottom: 20px;")
        content_layout.addWidget(subtitle_label, alignment=Qt.AlignmentFlag.AlignCenter)

        cropped_data = self.get_cropped_svg_data(most_expensive_svg_path, CROP_SIZE)
        svg_widget = QSvgWidget()
        if cropped_data:
            svg_widget.load(cropped_data)
        svg_widget.setFixedSize(CROP_SIZE, CROP_SIZE)
        content_layout.addWidget(svg_widget, alignment=Qt.AlignmentFlag.AlignCenter)

        price = self.extract_price(most_expensive_svg_path)
        price_label = QLabel(f"Price: {price}")
        price_label.setStyleSheet("color: green; font-size: 24px; font-weight: bold; margin-bottom: 15px;")
        price_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(price_label)

        # 2. Handle "Other Outputs"
        other_outputs = all_svg_files_to_display[1:]
        sorted_other_outputs = sorted(
            other_outputs,
            key=lambda path: int(p) if (p := self.extract_price(path)).isdigit() else 0,
            reverse=True
        )
        num_other_outputs = len(sorted_other_outputs)

        if num_other_outputs > 0:
            if num_other_outputs > 5:
                display_title = "Top 5"
                outputs_to_show = sorted_other_outputs[:5]
            else:
                display_title = f"Top {num_other_outputs}"
                outputs_to_show = sorted_other_outputs

            other_subtitle_label = QLabel(display_title)
            other_subtitle_label.setStyleSheet("font-size: 36px; font-weight: bold; margin-top: 50px; margin-bottom: 20px;")
            content_layout.addWidget(other_subtitle_label, alignment=Qt.AlignmentFlag.AlignCenter)

            for svg_path in outputs_to_show:
                cropped_data = self.get_cropped_svg_data(svg_path, CROP_SIZE)
                svg_widget = QSvgWidget()
                if cropped_data:
                    svg_widget.load(cropped_data)
                svg_widget.setFixedSize(CROP_SIZE, CROP_SIZE)
                content_layout.addWidget(svg_widget, alignment=Qt.AlignmentFlag.AlignCenter)

                price = self.extract_price(svg_path)
                price_label = QLabel(f"Price: {price}")
                price_label.setStyleSheet("color: green; font-size: 24px; font-weight: bold; margin-bottom: 30px;")
                price_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                content_layout.addWidget(price_label)

        # Add final success message
        content_layout.addStretch(1)
        short_path = "/".join(saved_svg_path.parts[-2:])
        success_label = QLabel(f"Top SVG saved successfully in 'saved_svg' as: {short_path}")
        success_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        success_label.setStyleSheet("color: green; font-weight: bold; font-size: 16px;")
        success_label.setWordWrap(False)
        success_label.setMaximumWidth(1000)
        success_label.setContentsMargins(0, 0, 0, 20)
        content_layout.addWidget(success_label, alignment=Qt.AlignmentFlag.AlignCenter)
        
        scroll_area.setWidget(scroll_content_widget)

        self.page_layout.addWidget(scroll_area)

        # Resize the window after all widgets are added
        self.resize(800, 700)


class WebcamWindow(QWidget):
    captured = Signal(QPixmap, str)  # ✅ Accepts both QPixmap and file path

    def __init__(self):
        super().__init__()
        self.capture = None
        self.timer = None

        layout = QVBoxLayout()

        self.video_label = QLabel("Webcam Feed")
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.capture_button = QPushButton("Capture Image")
        self.capture_button.clicked.connect(self.capture_image)

        self.back_button = QPushButton("Back to Main Page")
        self.back_button.clicked.connect(self.go_back)

        layout.addWidget(self.video_label)
        layout.addWidget(self.capture_button)
        layout.addWidget(self.back_button)

        self.setLayout(layout)

        self.start_webcam()

    def start_webcam(self):
        self.capture = cv2.VideoCapture(0)
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        self.timer.start(30)

    def update_frame(self):
        ret, frame = self.capture.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = frame.shape
            bytes_per_line = ch * w
            q_img = QImage(frame.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
            self.video_label.setPixmap(QPixmap.fromImage(q_img))

    def capture_image(self):
        """ Captures an image, saves it with a unique name, and properly closes the webcam window """
        ret, frame = self.capture.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Get aspect ratio and resize while keeping width proportional
            h, w, ch = frame.shape
            new_width = int((500 / h) * w)  # Scale width proportionally
            frame = cv2.resize(frame, (new_width, 500))  # Resize with fixed height of 500px

            # ✅ Fix: Ensure cross-platform compatibility for file paths
            save_dir = get_path("captured_images")  # Convert to absolute, OS-correct path
            save_dir.mkdir(parents=True, exist_ok=True)  # Ensure the directory exists

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")  # Unique timestamp
            file_path = save_dir / f"capture_{timestamp}.jpg"  # ✅ Use Path() for proper path joining

            # ✅ Convert to string before passing to OpenCV
            cv2.imwrite(str(file_path), cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

            # Convert to QPixmap
            bytes_per_line = ch * new_width
            q_img = QImage(frame.data, new_width, 500, bytes_per_line, QImage.Format.Format_RGB888)
            captured_pixmap = QPixmap.fromImage(q_img)

            # ✅ Emit captured image and file path
            self.captured.emit(captured_pixmap, str(file_path))  # Convert path to string for PySide6

            # ✅ Properly stop the webcam and close the window
            self.close_webcam()


    def close_webcam(self):
        """ Stops the webcam feed and releases resources """
        if self.timer:
            self.timer.stop()  # ✅ Stop the update timer
        if self.capture:
            self.capture.release()  # ✅ Release the webcam
        self.close()  # ✅ Close the window properly

    def go_back(self):
        """ Closes the webcam feed and returns to the main page """
        self.close_webcam() 

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Image Processing App")
        self.resize(400, 400)

        self.main_page = SelectPage()
        self.setCentralWidget(self.main_page)


# Run the application
app = QApplication(sys.argv)
window = MainWindow()
window.show()
app.exec()
