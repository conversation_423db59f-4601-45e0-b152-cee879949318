from __future__ import print_function
from __future__ import division
from builtins import range
from past.utils import old_div
import numpy as np
from scipy import ndimage, optimize
import pdb
import matplotlib.pyplot as plt
import cv2
import matplotlib.patches as patches
import multiprocessing
import datetime
from functools import reduce
from scipy.spatial import distance as dist
import argparse
import numpy as np
from matplotlib import pyplot as plt



def get_bin_from_image(image, l, u, GaussianBlur_size, threshold_threshold):

    # black color boundaries [B, G, R]
    # l = 0
    # u = 100
    lower = [l, l, l]
    upper = [u, u, u]

    # create NumPy arrays from the boundaries
    lower = np.array(lower, dtype="uint8")
    upper = np.array(upper, dtype="uint8")

    # find the colors within the specified boundaries and apply
    # the mask
    blur = cv2.GaussianBlur(image,(GaussianBlur_size,GaussianBlur_size),0) #51
    mask = cv2.inRange(blur, lower, upper)
    output = cv2.bitwise_or(blur, blur)

    # blur = cv2.GaussianBlur(ima,(5,5),0)
    
    ret, thresh = cv2.threshold(mask, threshold_threshold, 255, 0) #40

    # cv2.imshow('mask', mask)
    # cv2.imshow('thresh', thresh)
    # cv2.imshow('output', output)

    
    # cv2.waitKey(0)

    stencil = np.zeros(image.shape)  # .astype(image.dtype)
    out_bin = stencil

    if (cv2.__version__[0] > '3'):
        contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    else:
        im2, contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)


    if len(contours) != 0:
        # draw in blue the contours that were founded

        # find the biggest countour (c) by the area
        c = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(c)
        cv2.drawContours(output, c, -1, 255, 3)
        # draw the biggest contour (c) in green
        cv2.rectangle(output, (x, y), (x + w, y + h), (0, 255, 0), 2)

        cntr_z = c
        color_z = [255, 255, 255]
        cv2.fillPoly(stencil, [cntr_z], color_z)

        out_bin = stencil[y - 50:y + h + 50, x - 50:x + w + 50]
    else:
        c = []

    # cv2.imwrite("sang_bin.png", out_bin)
    return out_bin, c, stencil, thresh
        





def save_contour_as_svg_file(c, width, height):
    f = open('path.svg', 'w+')
    f.write('<svg width="'+str(width)+'" height="'+str(height)+'" xmlns="http://www.w3.org/2000/svg">')
    f.write('<path d="M')

    for i in range(len(c)):
        #print(c[i][0])
        x, y = c[i][0]
        # print(x)
        f.write(str(x) + ' ' + str(y) + ' ')
    
    f.write('"/>')
    f.write('</svg>')
    f.close()



"""

image = cv2.imread('test1.jpg', 1)

scale_percent = 20

#calculate the 50 percent of original dimensions
width = int(image.shape[1] * scale_percent / 100)
height = int(image.shape[0] * scale_percent / 100)

# dsize
dsize = (width, height)

# resize image
output = cv2.resize(image, dsize)

# cv2.imshow('output', output)

# cv2.waitKey(0)

bin, con, out_put_pic= get_bin(output)

# cv2.imshow('bin', out_put_pic)
# cv2.waitKey(0)

cv2.drawContours(out_put_pic, con, -1, 255, 3)

cv2.imshow('bin_c', out_put_pic)
cv2.waitKey(0)

print(len(con))

c = con

# c = [[[10,10],[16,20],[40,30],[10,10]]]

p = 'M '

print(c)


for i in c:
    # print(i[0][0], ',', i[0][1])
    p += str(i[0][0])
    p += ' '
    p += str(i[0][1])
    p += ' '



x,y,w,h = cv2.boundingRect(c)
img = cv2.rectangle(out_put_pic,(x,y),(x+w,y+h),(0,255,0),2)
img = cv2.circle(img, (x,y), 5, (0,0,255))
cv2.imshow('out_put_pic', out_put_pic)
cv2.waitKey(0)


contour_list = []

for i in c:
    contour_list.append((i[0][0], i[0][1]))


print(contour_list)

sorted = sorted(contour_list, key=lambda element: (element[0], element[1]))

print(sorted)

smallest = sorted[1]

print(smallest)

smallest_x = smallest[0] - 20
smallest_y = smallest[1] - 20

print(smallest_x, smallest_y)

pp = 'M '

for i in c:
    # print(i[0][0], ',', i[0][1])
    pp += str(i[0][0] - smallest_x)
    pp += ' '
    pp += str(i[0][1] - smallest_y)
    pp += ' '

print(pp)


smallest_x_rect = x - 20
smallest_y_rect = y - 20


ppp = 'M '

for i in c:
    # print(i[0][0], ',', i[0][1])
    ppp += str(i[0][0] - smallest_x_rect)
    ppp += ' '
    ppp += str(i[0][1] - smallest_y_rect)
    ppp += ' '

print(ppp)


"""

